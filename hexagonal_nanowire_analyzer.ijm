/*
 * Hexagonal Nanowire Face Analyzer
 * 
 * This script detects hexagonal nanowire faces (bright spots) and allows the user
 * to select a ROI that is then fitted with 6 lines forming a hexagon.
 * The angles between neighboring lines can deviate from perfect 120° (±20° tolerance).
 * Opposing lines should be approximately parallel (with tolerance).
 * Calculates the difference between the fitted hexagonal area and original ROI area.
 */

// === CONFIGURATION ===
ANGLE_TOLERANCE = 20;  // Degrees tolerance from perfect 120°
PARALLEL_TOLERANCE = 10;  // Degrees tolerance for parallel lines
MIN_FACE_SIZE = 50;  // Minimum area for face detection
SHOW_INTERMEDIATE_STEPS = true;  // Show detection and fitting steps

// === INITIALIZATION ===
print("\\Clear");
print("=== Hexagonal Nanowire Face Analyzer ===");
print("Initialization...");

// Get current image
if (nImages == 0) {
    showMessage("Error", "No image is open. Please open an image first.");
    exit();
}

original = getImageID();
originalTitle = getTitle();
print("Processing image: " + originalTitle);

// === FACE DETECTION ===
print("\n--- Face Detection ---");

// Duplicate image for processing
selectImage(original);
run("8-bit");
run("Duplicate...", "title=Detection");
detectionID = getImageID();

// Enhance contrast to highlight bright faces
run("Enhance Contrast", "saturated=0.35");
run("Gaussian Blur...", "sigma=1");

// Create binary mask for bright regions
setAutoThreshold("Otsu dark");
run("Convert to Mask");
run("Fill Holes");

// Clean up small artifacts
run("Open");
run("Close-");

// Detect potential hexagonal faces
run("Set Measurements...", "area centroid bounding redirect=None decimal=3");
run("Analyze Particles...", "size=" + MIN_FACE_SIZE + "-Infinity show=Overlay add display exclude clear");

nFaces = nResults;
print("Detected " + nFaces + " potential hexagonal faces");

if (nFaces == 0) {
    showMessage("No Faces Found", "No potential hexagonal faces detected.\nTry adjusting the MIN_FACE_SIZE parameter.");
    exit();
}

// === USER SELECTION ===
print("\n--- User Selection ---");

// Switch back to original image
selectImage(original);
roiManager("Show All with labels");

// Wait for user to select a face
waitForUser("Face Selection", 
    "Detected " + nFaces + " potential faces (shown as overlays).\n \n" +
    "Please select a ROI around the hexagonal face you want to analyze.\n" +
    "You can:\n" +
    "- Draw a new selection around a face\n" +
    "- Select an existing ROI from the ROI Manager\n \n" +
    "Click OK when ready to proceed with hexagon fitting.");

// Get the selected ROI
if (selectionType() == -1) {
    showMessage("Error", "No ROI selected. Please select a region around a hexagonal face.");
    exit();
}

// Store original ROI area
getSelectionBounds(roiX, roiY, roiWidth, roiHeight);
originalArea = getValue("Area");
print("Selected ROI area: " + originalArea + " pixels²");

// === HEXAGON FITTING ===
print("\n--- Hexagon Fitting ---");

// Get ROI coordinates for edge detection
getSelectionCoordinates(xCoords, yCoords);
nPoints = xCoords.length;

if (nPoints < 6) {
    showMessage("Error", "Selected ROI has too few points for hexagon fitting.");
    exit();
}

// Find edge points using convex hull approach
run("Convex Hull");
getSelectionCoordinates(hullX, hullY);
nHullPoints = hullX.length;

print("Convex hull has " + nHullPoints + " points");

// === LINE FITTING ALGORITHM ===
print("Fitting hexagon lines...");

// Calculate centroid of the shape
centroidX = 0;
centroidY = 0;
for (i = 0; i < nHullPoints; i++) {
    centroidX += hullX[i];
    centroidY += hullY[i];
}
centroidX /= nHullPoints;
centroidY /= nHullPoints;

// Convert points to polar coordinates relative to centroid
angles = newArray(nHullPoints);
distances = newArray(nHullPoints);

for (i = 0; i < nHullPoints; i++) {
    dx = hullX[i] - centroidX;
    dy = hullY[i] - centroidY;
    angles[i] = atan2(dy, dx) * 180 / PI;
    if (angles[i] < 0) angles[i] += 360;
    distances[i] = sqrt(dx*dx + dy*dy);
}

// Sort points by angle
for (i = 0; i < nHullPoints - 1; i++) {
    for (j = i + 1; j < nHullPoints; j++) {
        if (angles[i] > angles[j]) {
            // Swap angles
            temp = angles[i];
            angles[i] = angles[j];
            angles[j] = temp;
            // Swap coordinates
            temp = hullX[i];
            hullX[i] = hullX[j];
            hullX[j] = temp;
            temp = hullY[i];
            hullY[i] = hullY[j];
            hullY[j] = temp;
            // Swap distances
            temp = distances[i];
            distances[i] = distances[j];
            distances[j] = temp;
        }
    }
}

// === HEXAGON VERTEX DETECTION ===
// Group points into 6 sectors (60° each) and find representative points
hexVerticesX = newArray(6);
hexVerticesY = newArray(6);
sectorSize = 360.0 / 6.0;

for (sector = 0; sector < 6; sector++) {
    sectorStart = sector * sectorSize;
    sectorEnd = (sector + 1) * sectorSize;
    
    // Find points in this sector
    maxDist = 0;
    bestX = centroidX;
    bestY = centroidY;
    
    for (i = 0; i < nHullPoints; i++) {
        angle = angles[i];
        // Handle wrap-around at 360°
        if (sector == 5 && angle < sectorSize) angle += 360;
        
        if (angle >= sectorStart && angle < sectorEnd) {
            if (distances[i] > maxDist) {
                maxDist = distances[i];
                bestX = hullX[i];
                bestY = hullY[i];
            }
        }
    }
    
    hexVerticesX[sector] = bestX;
    hexVerticesY[sector] = bestY;
}

print("Hexagon vertices identified");

// === CREATE FITTED HEXAGON ===
// Create polygon from vertices
makeSelection("polygon", hexVerticesX, hexVerticesY);
fittedArea = getValue("Area");

print("Fitted hexagon area: " + fittedArea + " pixels²");

// === ANGLE VALIDATION ===
print("\n--- Angle Validation ---");

validHexagon = true;
angleErrors = newArray(6);

for (i = 0; i < 6; i++) {
    // Calculate angle between three consecutive vertices
    p1 = i;
    p2 = (i + 1) % 6;
    p3 = (i + 2) % 6;
    
    // Vectors from p2 to p1 and p2 to p3
    v1x = hexVerticesX[p1] - hexVerticesX[p2];
    v1y = hexVerticesY[p1] - hexVerticesY[p2];
    v2x = hexVerticesX[p3] - hexVerticesX[p2];
    v2y = hexVerticesY[p3] - hexVerticesY[p2];
    
    // Calculate angle using dot product
    dot = v1x * v2x + v1y * v2y;
    mag1 = sqrt(v1x * v1x + v1y * v1y);
    mag2 = sqrt(v2x * v2x + v2y * v2y);
    
    if (mag1 > 0 && mag2 > 0) {
        cosAngle = dot / (mag1 * mag2);
        // Clamp to valid range for acos
        if (cosAngle > 1) cosAngle = 1;
        if (cosAngle < -1) cosAngle = -1;
        angle = acos(cosAngle) * 180 / PI;
        
        // Interior angle of hexagon should be 120°
        angleError = abs(angle - 120);
        angleErrors[i] = angleError;
        
        print("Angle " + (i+1) + ": " + d2s(angle, 1) + "° (error: " + d2s(angleError, 1) + "°)");
        
        if (angleError > ANGLE_TOLERANCE) {
            validHexagon = false;
        }
    }
}

// === PARALLEL LINE VALIDATION ===
print("\n--- Parallel Line Validation ---");

for (i = 0; i < 3; i++) {
    // Check if opposing sides are parallel
    side1_start = i;
    side1_end = (i + 1) % 6;
    side2_start = (i + 3) % 6;
    side2_end = (i + 4) % 6;
    
    // Calculate slopes
    dx1 = hexVerticesX[side1_end] - hexVerticesX[side1_start];
    dy1 = hexVerticesY[side1_end] - hexVerticesY[side1_start];
    dx2 = hexVerticesX[side2_end] - hexVerticesX[side2_start];
    dy2 = hexVerticesY[side2_end] - hexVerticesY[side2_start];
    
    // Calculate angles of the lines
    angle1 = atan2(dy1, dx1) * 180 / PI;
    angle2 = atan2(dy2, dx2) * 180 / PI;
    
    // Normalize angles to [0, 180)
    if (angle1 < 0) angle1 += 180;
    if (angle2 < 0) angle2 += 180;
    
    // Calculate difference (should be close to 0° for parallel lines)
    angleDiff = abs(angle1 - angle2);
    if (angleDiff > 90) angleDiff = 180 - angleDiff;
    
    print("Opposing sides " + (i+1) + ": angle difference = " + d2s(angleDiff, 1) + "°");
    
    if (angleDiff > PARALLEL_TOLERANCE) {
        validHexagon = false;
    }
}

// === RESULTS ===
print("\n--- Results ---");

areaDifference = abs(fittedArea - originalArea);
areaRatio = fittedArea / originalArea;

print("Original ROI area: " + d2s(originalArea, 2) + " pixels²");
print("Fitted hexagon area: " + d2s(fittedArea, 2) + " pixels²");
print("Area difference: " + d2s(areaDifference, 2) + " pixels²");
print("Area ratio (fitted/original): " + d2s(areaRatio, 3));

if (validHexagon) {
    print("✓ Hexagon validation: PASSED");
} else {
    print("✗ Hexagon validation: FAILED (angles or parallelism outside tolerance)");
}

// === VISUALIZATION ===
if (SHOW_INTERMEDIATE_STEPS) {
    // Add fitted hexagon to ROI Manager
    roiManager("Add");
    roiManager("Select", roiManager("Count") - 1);
    roiManager("Rename", "Fitted_Hexagon");
    
    // Show both original and fitted ROI
    roiManager("Show All");
}

// === SUMMARY ===
print("\n=== Analysis Complete ===");
print("Hexagon fitting completed successfully.");
print("Check the ROI Manager for the fitted hexagon overlay.");

// Clean up detection image
selectImage(detectionID);
close();

selectImage(original);
